{"nodes": [{"id": "Space_0", "position": {"x": 1200.4889076713946, "y": 130.21766429926652}, "type": "customNode", "data": {"id": "Space_0", "label": "Space", "version": 1, "name": "Space", "type": "UPDLSpace", "baseClasses": ["UPDLSpace", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Root node for a 3D space that contains global space settings", "inputParams": [{"name": "spaceName", "type": "string", "label": "Space Name", "description": "Name of the space", "default": "My Space", "id": "Space_0-input-spaceName-string"}, {"name": "spaceType", "type": "options", "label": "Space Type", "description": "Classification of the space", "options": [{"label": "Root", "name": "root"}, {"label": "<PERSON><PERSON><PERSON>", "name": "module"}, {"label": "Block", "name": "block"}], "default": "root", "additionalParams": true, "id": "Space_0-input-spaceType-options"}, {"name": "settings", "type": "json", "label": "Settings", "description": "Generic configuration object", "optional": true, "additionalParams": true, "id": "Space_0-input-settings-json"}, {"name": "backgroundColor", "type": "string", "label": "Background Color", "description": "Background color of the space (hex code)", "default": "", "optional": true, "additionalParams": true, "id": "Space_0-input-backgroundColor-string"}, {"name": "skybox", "type": "boolean", "label": "Enable Skybox", "description": "Whether to use a skybox", "default": false, "additionalParams": true, "id": "Space_0-input-skybox-boolean"}, {"name": "skyboxTexture", "type": "string", "label": "Skybox Texture", "description": "URL to the skybox texture (optional)", "optional": true, "additionalParams": true, "show": {"inputs.skybox": [true]}, "id": "Space_0-input-skyboxTexture-string"}, {"name": "fog", "type": "boolean", "label": "Enable Fog", "description": "Whether to use fog effect", "default": false, "additionalParams": true, "id": "Space_0-input-fog-boolean"}, {"name": "fogColor", "type": "string", "label": "Fog Color", "description": "Color of the fog (hex code)", "default": "", "optional": true, "additionalParams": true, "show": {"inputs.fog": [true]}, "id": "Space_0-input-fogColor-string"}, {"name": "fogDensity", "type": "number", "label": "Fog Density", "description": "Density of the fog (0-1)", "default": 0.1, "min": 0, "max": 1, "step": 0.01, "additionalParams": true, "show": {"inputs.fog": [true]}, "id": "Space_0-input-fogDensity-number"}, {"name": "isRootNode", "type": "boolean", "label": "Is Root Node", "description": "Space must be the root node of a UPDL flow", "default": true, "hidden": true, "id": "Space_0-input-isRootNode-boolean"}, {"name": "showPoints", "type": "boolean", "label": "Show Points Counter", "description": "Display points counter in the AR interface", "default": false, "additionalParams": true, "id": "Space_0-input-showPoints-boolean"}, {"name": "collectLeadName", "type": "boolean", "label": "Collect Name", "description": "Collect participant name for quiz data", "default": false, "additionalParams": true, "id": "Space_0-input-collectLeadName-boolean"}, {"name": "collectLeadEmail", "type": "boolean", "label": "Collect Email", "description": "Collect participant email for quiz data", "default": false, "additionalParams": true, "id": "Space_0-input-collectLeadEmail-boolean"}, {"name": "collectLeadPhone", "type": "boolean", "label": "Collect Phone", "description": "Collect participant phone for quiz data", "default": false, "additionalParams": true, "id": "Space_0-input-collectLeadPhone-boolean"}], "inputAnchors": [{"label": "Spaces", "name": "spaces", "type": "UPDLSpace", "description": "Connect Space nodes to create space chains", "list": true, "optional": true, "id": "Space_0-input-spaces-UPDLSpace"}, {"label": "Datas", "name": "data", "type": "UPDLData", "description": "Connect Data nodes for quiz questions, answers, and logic", "list": true, "optional": true, "id": "Space_0-input-data-UPDLData"}, {"label": "Objects", "name": "objects", "type": "UPDLObject", "description": "Connect Object nodes to add them to the space", "list": true, "optional": true, "id": "Space_0-input-objects-UPDLObject"}, {"label": "Lights", "name": "lights", "type": "UPDLLight", "description": "Connect Light nodes to add them to the space", "list": true, "optional": true, "id": "Space_0-input-lights-UPDLLight"}, {"label": "Cameras", "name": "cameras", "type": "UPDLCamera", "description": "Connect Camera nodes to add them to the space", "list": true, "optional": true, "id": "Space_0-input-cameras-UPDLCamera"}, {"label": "Entities", "name": "entities", "type": "UPDLEntity", "description": "Connect Entity nodes to add them to the space", "list": true, "optional": true, "id": "Space_0-input-entities-UPDLEntity"}, {"label": "Universo", "name": "universo", "type": "UPDLUniverso", "description": "Connect a Universo node for global settings", "optional": true, "id": "Space_0-input-universo-UPDLUniverso"}], "inputs": {"spaces": ["{{Space_1.data.instance}}"], "spaceName": "<PERSON><PERSON>", "spaceType": "root", "settings": "", "backgroundColor": "#000011", "skybox": "", "skyboxTexture": "", "fog": "", "fogColor": "", "fogDensity": 0.1, "isRootNode": true, "showPoints": "", "collectLeadName": "", "collectLeadEmail": "", "collectLeadPhone": "", "data": ["{{Data_0.data.instance}}", "{{Data_1.data.instance}}"], "objects": "", "lights": "", "cameras": "", "entities": ["{{Entity_0.data.instance}}", "{{Entity_1.data.instance}}", "{{Entity_2.data.instance}}", "{{Entity_3.data.instance}}", "{{Entity_4.data.instance}}", "{{Entity_5.data.instance}}", "{{Entity_6.data.instance}}"], "universo": ""}, "outputAnchors": [{"id": "Space_0-output-Space-UPDLSpace|UPDLNode", "name": "Space", "label": "UPDLSpace", "description": "Root node for a 3D space that contains global space settings", "type": "UPDLSpace | UPDLNode"}], "outputs": {"Space": ""}, "selected": false}, "width": 300, "height": 686, "selected": false, "dragging": false, "positionAbsolute": {"x": 1200.4889076713946, "y": 130.21766429926652}}, {"id": "Entity_0", "position": {"x": 616.6624567374342, "y": 130.38029240773196}, "type": "customNode", "data": {"id": "Entity_0", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_0-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_0-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_0-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_0-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_0-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_0.data.instance}}", "{{Component_1.data.instance}}", "{{Component_2.data.instance}}"], "events": "", "entityType": "ship", "transform": "{\"pos\":[0,2,0],\"rot\":[0,0,0],\"scale\":[4,4,12]}", "tags": "player,ship"}, "outputAnchors": [{"id": "Entity_0-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": 616.6624567374342, "y": 130.38029240773196}, "dragging": false}, {"id": "Component_0", "position": {"x": 198.29357315302036, "y": 131.0996339574593}, "type": "customNode", "data": {"id": "Component_0", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_0-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_0-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_0-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_0-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_0-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_0-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_0-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_0-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_0-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_0-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_0-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_0-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_0-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_0-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_0-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "box", "color": "#00ff00", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_0-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": 198.29357315302036, "y": 131.0996339574593}, "dragging": false}, {"id": "Component_1", "position": {"x": 193.88763849899385, "y": 500.96498076251964}, "type": "customNode", "data": {"id": "Component_1", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_1-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_1-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_1-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_1-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_1-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_1-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_1-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_1-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_1-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_1-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_1-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_1-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_1-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_1-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_1-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "inventory", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "0", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_1-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": 193.88763849899385, "y": 500.96498076251964}, "dragging": false}, {"id": "Component_2", "position": {"x": 611.6728146616375, "y": 641.4591107995147}, "type": "customNode", "data": {"id": "Component_2", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_2-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_2-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_2-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_2-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_2-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_2-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_2-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_2-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_2-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_2-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_2-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_2-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_2-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_2-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_2-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "weapon", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_2-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": 611.6728146616375, "y": 641.4591107995147}, "dragging": false}, {"id": "Entity_1", "position": {"x": -348.05258294349096, "y": 133.90081559531154}, "type": "customNode", "data": {"id": "Entity_1", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_1-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_1-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_1-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_1-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_1-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_3.data.instance}}", "{{Component_4.data.instance}}"], "events": "", "entityType": "station", "transform": "{\"pos\":[200,0,90],\"rot\":[0,0,0],\"scale\":[80,40,120]}", "tags": ""}, "outputAnchors": [{"id": "Entity_1-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": -348.05258294349096, "y": 133.90081559531154}, "dragging": false}, {"id": "Component_3", "position": {"x": -762.527640766718, "y": 134.7968479058013}, "type": "customNode", "data": {"id": "Component_3", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_3-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_3-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_3-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_3-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_3-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_3-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_3-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_3-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_3-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_3-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_3-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_3-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_3-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_3-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_3-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_3-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -762.527640766718, "y": 134.7968479058013}, "dragging": false}, {"id": "Component_4", "position": {"x": -761.3788543673392, "y": 499.47821937090157}, "type": "customNode", "data": {"id": "Component_4", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_4-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_4-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_4-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_4-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_4-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_4-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_4-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_4-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_4-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_4-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_4-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_4-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_4-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_4-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_4-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "trading", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": "20", "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_4-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -761.3788543673392, "y": 499.47821937090157}, "dragging": false}, {"id": "Entity_2", "position": {"x": -1299.8463648561196, "y": 134.62560459662646}, "type": "customNode", "data": {"id": "Entity_2", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_2-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_2-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_2-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_2-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_2-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_5.data.instance}}", "{{Component_9.data.instance}}"], "events": "", "entityType": "asteroid", "transform": "{\"pos\":[-10,0,5],\"rot\":[0,0,0],\"scale\":[3,8,3]}", "tags": "asteroid"}, "outputAnchors": [{"id": "Entity_2-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": -1299.8463648561196, "y": 134.62560459662646}, "dragging": false}, {"id": "Component_5", "position": {"x": -1721.9280717796555, "y": 137.70776959961913}, "type": "customNode", "data": {"id": "Component_5", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_5-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_5-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_5-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_5-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_5-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_5-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_5-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_5-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_5-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_5-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_5-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_5-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_5-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_5-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_5-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "sphere", "color": "#8B4513", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_5-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1721.9280717796555, "y": 137.70776959961913}, "dragging": false}, {"id": "Entity_3", "position": {"x": 624.6539927610588, "y": 1161.308384308948}, "type": "customNode", "data": {"id": "Entity_3", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_3-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_3-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_3-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_3-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_3-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_7.data.instance}}", "{{Component_8.data.instance}}"], "events": "", "entityType": "gate", "transform": "{\"pos\":[0,10,10],\"rot\":[90,0,0],\"scale\":[10,10,10]}", "tags": ""}, "outputAnchors": [{"id": "Entity_3-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": 624.6539927610588, "y": 1161.308384308948}, "dragging": false}, {"id": "Component_7", "position": {"x": 187.81593503303208, "y": 1172.0401940547106}, "type": "customNode", "data": {"id": "Component_7", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_7-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_7-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_7-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_7-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_7-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_7-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_7-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_7-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_7-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_7-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_7-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_7-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_7-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_7-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_7-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "torus", "color": "#FFD700", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_7-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": 187.81593503303208, "y": 1172.0401940547106}, "dragging": false}, {"id": "Component_8", "position": {"x": 190.4751088449293, "y": 1591.5818863703707}, "type": "customNode", "data": {"id": "Component_8", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_8-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_8-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_8-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_8-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_8-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_8-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_8-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_8-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_8-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_8-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_8-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_8-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_8-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_8-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_8-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "portal", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_8-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": 190.4751088449293, "y": 1591.5818863703707}, "dragging": false}, {"id": "Data_0", "position": {"x": 1742.4541656597937, "y": 125.34006541563721}, "type": "customNode", "data": {"id": "Data_0", "label": "Data", "version": 1, "name": "Data", "type": "UPDLData", "baseClasses": ["UPDLData", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Universal node for quiz data, questions, answers, and transitions", "inputParams": [{"name": "dataName", "type": "string", "label": "Data Name", "description": "Name of the data element", "default": "My Data", "id": "Data_0-input-dataName-string"}, {"name": "key", "type": "string", "label": "Key", "description": "Data key identifier", "optional": true, "additionalParams": true, "id": "Data_0-input-key-string"}, {"name": "scope", "type": "options", "label": "<PERSON><PERSON>", "description": "Variable scope", "options": [{"label": "Local", "name": "Local"}, {"label": "Space", "name": "Space"}, {"label": "Global", "name": "Global"}], "default": "Local", "additionalParams": true, "id": "Data_0-input-scope-options"}, {"name": "value", "type": "string", "label": "Value", "description": "Stored value", "optional": true, "additionalParams": true, "id": "Data_0-input-value-string"}, {"name": "dataType", "type": "options", "label": "Data Type", "description": "Type of data this node represents", "options": [{"label": "Question", "name": "question", "description": "Quiz question"}, {"label": "Answer", "name": "answer", "description": "Quiz answer option"}, {"label": "Intro", "name": "intro", "description": "Introduction screen"}, {"label": "Transition", "name": "transition", "description": "Screen transition"}], "default": "question", "id": "Data_0-input-dataType-options"}, {"name": "content", "type": "string", "label": "Content", "description": "Main content text (question text, answer text, etc.)", "multiline": true, "rows": 3, "default": "", "id": "Data_0-input-content-string"}, {"name": "isCorrect", "type": "boolean", "label": "Is Correct Answer", "description": "Mark this as the correct answer (only for answer type)", "default": false, "additionalParams": true, "show": {"inputs.dataType": ["answer"]}, "id": "Data_0-input-isCorrect-boolean"}, {"name": "nextSpace", "type": "string", "label": "Next Space ID", "description": "ID of the next space to transition to", "optional": true, "additionalParams": true, "show": {"inputs.dataType": ["transition", "answer"]}, "id": "Data_0-input-nextSpace-string"}, {"name": "userInputType", "type": "options", "label": "User Input Type", "description": "Type of user input expected", "options": [{"label": "<PERSON><PERSON>", "name": "button", "description": "Simple button interaction"}, {"label": "Text Input", "name": "text", "description": "Text input field"}, {"label": "None", "name": "none", "description": "No user input"}], "default": "button", "additionalParams": true, "show": {"inputs.dataType": ["intro", "transition"]}, "id": "Data_0-input-userInputType-options"}, {"name": "enablePoints", "type": "boolean", "label": "Enable Points", "description": "Enable point calculation for this data element", "default": false, "additionalParams": true, "id": "Data_0-input-enablePoints-boolean"}, {"name": "pointsValue", "type": "number", "label": "Points Value", "description": "Points to add/subtract when interacting with this element (-100 to +100)", "default": 1, "min": -100, "max": 100, "additionalParams": true, "show": {"inputs.enablePoints": [true]}, "id": "Data_0-input-pointsValue-number"}], "inputAnchors": [{"label": "Datas", "name": "datas", "type": "UPDLData", "description": "Connect Data nodes to create data chains", "list": true, "optional": true, "id": "Data_0-input-datas-UPDLData"}, {"label": "Objects", "name": "objects", "type": "UPDLObject", "description": "Connect Object nodes to associate with this data", "list": true, "optional": true, "id": "Data_0-input-objects-UPDLObject"}], "inputs": {"datas": "", "dataName": "Inmo Currency", "key": "player_inmo", "scope": "Global", "value": "100", "dataType": "question", "content": "", "isCorrect": "", "nextSpace": "", "userInputType": "button", "enablePoints": "", "pointsValue": 1, "objects": ""}, "outputAnchors": [{"id": "Data_0-output-Data-UPDLData|UPDLNode", "name": "Data", "label": "UPDLData", "description": "Universal node for quiz data, questions, answers, and transitions", "type": "UPDLData | UPDLNode"}], "outputs": {"Data": ""}, "selected": false}, "width": 300, "height": 705, "selected": false, "dragging": false, "positionAbsolute": {"x": 1742.4541656597937, "y": 125.34006541563721}}, {"id": "Data_1", "position": {"x": 2135.5912488334916, "y": 115.48082822005858}, "type": "customNode", "data": {"id": "Data_1", "label": "Data", "version": 1, "name": "Data", "type": "UPDLData", "baseClasses": ["UPDLData", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Universal node for quiz data, questions, answers, and transitions", "inputParams": [{"name": "dataName", "type": "string", "label": "Data Name", "description": "Name of the data element", "default": "My Data", "id": "Data_1-input-dataName-string"}, {"name": "key", "type": "string", "label": "Key", "description": "Data key identifier", "optional": true, "additionalParams": true, "id": "Data_1-input-key-string"}, {"name": "scope", "type": "options", "label": "<PERSON><PERSON>", "description": "Variable scope", "options": [{"label": "Local", "name": "Local"}, {"label": "Space", "name": "Space"}, {"label": "Global", "name": "Global"}], "default": "Local", "additionalParams": true, "id": "Data_1-input-scope-options"}, {"name": "value", "type": "string", "label": "Value", "description": "Stored value", "optional": true, "additionalParams": true, "id": "Data_1-input-value-string"}, {"name": "dataType", "type": "options", "label": "Data Type", "description": "Type of data this node represents", "options": [{"label": "Question", "name": "question", "description": "Quiz question"}, {"label": "Answer", "name": "answer", "description": "Quiz answer option"}, {"label": "Intro", "name": "intro", "description": "Introduction screen"}, {"label": "Transition", "name": "transition", "description": "Screen transition"}], "default": "question", "id": "Data_1-input-dataType-options"}, {"name": "content", "type": "string", "label": "Content", "description": "Main content text (question text, answer text, etc.)", "multiline": true, "rows": 3, "default": "", "id": "Data_1-input-content-string"}, {"name": "isCorrect", "type": "boolean", "label": "Is Correct Answer", "description": "Mark this as the correct answer (only for answer type)", "default": false, "additionalParams": true, "show": {"inputs.dataType": ["answer"]}, "id": "Data_1-input-isCorrect-boolean"}, {"name": "nextSpace", "type": "string", "label": "Next Space ID", "description": "ID of the next space to transition to", "optional": true, "additionalParams": true, "show": {"inputs.dataType": ["transition", "answer"]}, "id": "Data_1-input-nextSpace-string"}, {"name": "userInputType", "type": "options", "label": "User Input Type", "description": "Type of user input expected", "options": [{"label": "<PERSON><PERSON>", "name": "button", "description": "Simple button interaction"}, {"label": "Text Input", "name": "text", "description": "Text input field"}, {"label": "None", "name": "none", "description": "No user input"}], "default": "button", "additionalParams": true, "show": {"inputs.dataType": ["intro", "transition"]}, "id": "Data_1-input-userInputType-options"}, {"name": "enablePoints", "type": "boolean", "label": "Enable Points", "description": "Enable point calculation for this data element", "default": false, "additionalParams": true, "id": "Data_1-input-enablePoints-boolean"}, {"name": "pointsValue", "type": "number", "label": "Points Value", "description": "Points to add/subtract when interacting with this element (-100 to +100)", "default": 1, "min": -100, "max": 100, "additionalParams": true, "show": {"inputs.enablePoints": [true]}, "id": "Data_1-input-pointsValue-number"}], "inputAnchors": [{"label": "Datas", "name": "datas", "type": "UPDLData", "description": "Connect Data nodes to create data chains", "list": true, "optional": true, "id": "Data_1-input-datas-UPDLData"}, {"label": "Objects", "name": "objects", "type": "UPDLObject", "description": "Connect Object nodes to associate with this data", "list": true, "optional": true, "id": "Data_1-input-objects-UPDLObject"}], "inputs": {"datas": "", "dataName": "Cargo Mass", "key": "cargo_mass", "scope": "Local", "value": "0", "dataType": "question", "content": "", "isCorrect": "", "nextSpace": "", "userInputType": "button", "enablePoints": "", "pointsValue": 1, "objects": ""}, "outputAnchors": [{"id": "Data_1-output-Data-UPDLData|UPDLNode", "name": "Data", "label": "UPDLData", "description": "Universal node for quiz data, questions, answers, and transitions", "type": "UPDLData | UPDLNode"}], "outputs": {"Data": ""}, "selected": false}, "width": 300, "height": 705, "selected": false, "dragging": false, "positionAbsolute": {"x": 2135.5912488334916, "y": 115.48082822005858}}, {"id": "Component_9", "position": {"x": -1730.595515849034, "y": 550.97171032456}, "type": "customNode", "data": {"id": "Component_9", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_9-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_9-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_9-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_9-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_9-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_9-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_9-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_9-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_9-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_9-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_9-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_9-input-maxYield-number"}, {"name": "asteroidVolume", "label": "Asteroid Volume (m³)", "type": "number", "description": "Physical volume of the asteroid", "default": 5, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_9-input-asteroidVolume-number"}, {"name": "hardness", "label": "Hardness", "type": "number", "description": "Mining difficulty (1=soft, 5=very hard)", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_9-input-hardness-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_9-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_9-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_9-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "mineable", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": "4", "asteroidVolume": "8", "hardness": 1, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_9-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1730.595515849034, "y": 550.97171032456}, "dragging": false}, {"id": "Entity_4", "position": {"x": -1293.2386639423528, "y": 995.9679610200532}, "type": "customNode", "data": {"id": "Entity_4", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_4-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_4-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_4-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_4-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_4-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_6.data.instance}}", "{{Component_10.data.instance}}"], "events": "", "entityType": "asteroid", "transform": "{\"pos\":[-20,-4,5],\"rot\":[0,0,0],\"scale\":[7,8,5]}", "tags": "asteroid"}, "outputAnchors": [{"id": "Entity_4-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": -1293.2386639423528, "y": 995.9679610200532}, "dragging": false}, {"id": "Component_6", "position": {"x": -1735.3080645895266, "y": 998.9160879535171}, "type": "customNode", "data": {"id": "Component_6", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_6-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_6-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_6-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_6-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_6-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_6-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_6-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_6-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_6-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_6-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_6-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_6-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_6-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_6-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_6-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "sphere", "color": "#803402", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_6-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1735.3080645895266, "y": 998.9160879535171}, "dragging": false}, {"id": "Component_10", "position": {"x": -1733.8436460900357, "y": 1418.2591462197797}, "type": "customNode", "data": {"id": "Component_10", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_10-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_10-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_10-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_10-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_10-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_10-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_10-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_10-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_10-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_10-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_10-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_10-input-maxYield-number"}, {"name": "asteroidVolume", "label": "Asteroid Volume (m³)", "type": "number", "description": "Physical volume of the asteroid", "default": 5, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_10-input-asteroidVolume-number"}, {"name": "hardness", "label": "Hardness", "type": "number", "description": "Mining difficulty (1=soft, 5=very hard)", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_10-input-hardness-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_10-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_10-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_10-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "mineable", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": "4", "asteroidVolume": "8", "hardness": 1, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_10-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1733.8436460900357, "y": 1418.2591462197797}, "dragging": false}, {"id": "Entity_5", "position": {"x": -1285.5118765180982, "y": 1870.5919386889377}, "type": "customNode", "data": {"id": "Entity_5", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_5-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_5-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_5-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_5-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_5-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_11.data.instance}}", "{{Component_12.data.instance}}"], "events": "", "entityType": "asteroid", "transform": "{\"pos\":[-4,-10,5],\"rot\":[0,0,30],\"scale\":[7,3,5]}", "tags": "asteroid"}, "outputAnchors": [{"id": "Entity_5-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": -1285.5118765180982, "y": 1870.5919386889377}, "dragging": false}, {"id": "Component_11", "position": {"x": -1744.1633435476297, "y": 1875.4206002580195}, "type": "customNode", "data": {"id": "Component_11", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_11-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_11-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_11-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_11-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_11-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_11-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_11-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_11-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_11-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_11-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_11-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_11-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_11-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_11-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_11-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "sphere", "color": "#602302", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_11-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1744.1633435476297, "y": 1875.4206002580195}, "dragging": false}, {"id": "Component_12", "position": {"x": -1752.271449320497, "y": 2275.2952842705236}, "type": "customNode", "data": {"id": "Component_12", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_12-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_12-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_12-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_12-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_12-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_12-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_12-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_12-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_12-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_12-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_12-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_12-input-maxYield-number"}, {"name": "asteroidVolume", "label": "Asteroid Volume (m³)", "type": "number", "description": "Physical volume of the asteroid", "default": 5, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_12-input-asteroidVolume-number"}, {"name": "hardness", "label": "Hardness", "type": "number", "description": "Mining difficulty (1=soft, 5=very hard)", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_12-input-hardness-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_12-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_12-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_12-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "mineable", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": "4", "asteroidVolume": "8", "hardness": 1, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_12-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1752.271449320497, "y": 2275.2952842705236}, "dragging": false}, {"id": "Entity_6", "position": {"x": -1286.8419737840647, "y": 2725.5613145303023}, "type": "customNode", "data": {"id": "Entity_6", "label": "Entity", "version": 1, "name": "Entity", "type": "UPDLEntity", "baseClasses": ["UPDLEntity", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Entity instance with transform and tags", "inputParams": [{"name": "entityType", "type": "options", "label": "Entity Type", "description": "Semantic type for the export template", "options": [{"label": "Static Object", "name": "StaticObject"}, {"label": "Player", "name": "player"}, {"label": "Interactive", "name": "interactive"}, {"label": "Vehicle", "name": "vehicle"}, {"label": "Ship", "name": "ship"}, {"label": "Station", "name": "station"}, {"label": "Asteroid", "name": "asteroid"}, {"label": "Gate", "name": "gate"}], "default": "StaticObject", "id": "Entity_6-input-entityType-options"}, {"name": "transform", "type": "json", "label": "Transform", "description": "Position, rotation and scale as a JSON object", "optional": true, "additionalParams": true, "default": "{\"pos\":[0,0,0], \"rot\":[0,0,0], \"scale\":[1,1,1]}", "id": "Entity_6-input-transform-json"}, {"name": "tags", "type": "string", "label": "Tags", "description": "Tags for this entity", "list": true, "optional": true, "additionalParams": true, "id": "Entity_6-input-tags-string"}], "inputAnchors": [{"label": "Components", "name": "components", "type": "UPDLComponent", "description": "Connect Component nodes to add behavior to this entity", "list": true, "optional": true, "id": "Entity_6-input-components-UPDLComponent"}, {"label": "Events", "name": "events", "type": "UPDLEvent", "description": "Connect Event nodes to define interactions with this entity", "list": true, "optional": true, "id": "Entity_6-input-events-UPDLEvent"}], "inputs": {"components": ["{{Component_13.data.instance}}", "{{Component_14.data.instance}}"], "events": "", "entityType": "asteroid", "transform": "{\"pos\":[4,-12,5],\"rot\":[0,0,30],\"scale\":[6,5,5]}", "tags": "asteroid"}, "outputAnchors": [{"id": "Entity_6-output-Entity-UPDLEntity|UPDLNode", "name": "Entity", "label": "UPDLEntity", "description": "Entity instance with transform and tags", "type": "UPDLEntity | UPDLNode"}], "outputs": {"Entity": ""}, "selected": false}, "width": 300, "height": 430, "selected": false, "positionAbsolute": {"x": -1286.8419737840647, "y": 2725.5613145303023}, "dragging": false}, {"id": "Component_13", "position": {"x": -1746.2093629866597, "y": 2729.768391565958}, "type": "customNode", "data": {"id": "Component_13", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_13-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_13-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_13-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_13-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_13-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_13-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_13-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_13-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_13-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_13-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_13-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_13-input-maxYield-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_13-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_13-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_13-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "render", "primitive": "sphere", "color": "#60CCCC", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": 3, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_13-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1746.2093629866597, "y": 2729.768391565958}, "dragging": false}, {"id": "Component_14", "position": {"x": -1742.794564116304, "y": 3139.786623861242}, "type": "customNode", "data": {"id": "Component_14", "label": "Component", "version": 1, "name": "Component", "type": "UPDLComponent", "baseClasses": ["UPDLComponent", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Component attached to an entity", "inputParams": [{"name": "componentType", "label": "Component Type", "type": "options", "description": "Type of component to attach", "options": [{"label": "Render", "name": "render"}, {"label": "<PERSON><PERSON><PERSON>", "name": "script"}, {"label": "Inventory", "name": "inventory"}, {"label": "Weapon", "name": "weapon"}, {"label": "Trading", "name": "trading"}, {"label": "Mineable", "name": "mineable"}, {"label": "Portal", "name": "portal"}], "default": "render", "id": "Component_14-input-componentType-options"}, {"name": "primitive", "label": "Primitive <PERSON><PERSON>pe", "type": "options", "description": "Basic 3D shape to render", "options": [{"label": "Box", "name": "box"}, {"label": "Sphere", "name": "sphere"}, {"label": "<PERSON><PERSON>", "name": "torus"}, {"label": "<PERSON><PERSON><PERSON>", "name": "cylinder"}], "default": "box", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_14-input-primitive-options"}, {"name": "color", "label": "Color (HEX)", "type": "string", "description": "Color of the rendered primitive", "default": "#FFFFFF", "additionalParams": true, "show": {"inputs.componentType": ["render"]}, "id": "Component_14-input-color-string"}, {"name": "scriptName", "label": "Script Name", "type": "string", "description": "Name of the script file (e.g., playerController.js)", "default": "myScript.js", "additionalParams": true, "show": {"inputs.componentType": ["script"]}, "id": "Component_14-input-scriptName-string"}, {"name": "maxCapacity", "label": "Max Capacity (m³)", "type": "number", "description": "Maximum cargo capacity in cubic meters", "default": 20, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_14-input-maxCapacity-number"}, {"name": "currentLoad", "label": "Current Load (m³)", "type": "number", "description": "Current cargo load in cubic meters", "default": 0, "additionalParams": true, "show": {"inputs.componentType": ["inventory"]}, "id": "Component_14-input-currentLoad-number"}, {"name": "fireRate", "label": "Fire Rate (shots/sec)", "type": "number", "description": "Weapon fire rate in shots per second", "default": 2, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_14-input-fireRate-number"}, {"name": "damage", "label": "Damage", "type": "number", "description": "Damage per shot", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["weapon"]}, "id": "Component_14-input-damage-number"}, {"name": "pricePerTon", "label": "Price Per Ton (Inmo)", "type": "number", "description": "Trading price per ton in Inmo currency", "default": 10, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_14-input-pricePerTon-number"}, {"name": "interactionRange", "label": "Interaction Range", "type": "number", "description": "Range for trading interaction", "default": 8, "additionalParams": true, "show": {"inputs.componentType": ["trading"]}, "id": "Component_14-input-interactionRange-number"}, {"name": "resourceType", "label": "Resource Type", "type": "string", "description": "Type of resource that can be mined", "default": "asteroidMass", "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_14-input-resourceType-string"}, {"name": "max<PERSON>ield", "label": "Max Yield (tons)", "type": "number", "description": "Maximum resource yield in tons", "default": 3, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_14-input-maxYield-number"}, {"name": "asteroidVolume", "label": "Asteroid Volume (m³)", "type": "number", "description": "Physical volume of the asteroid", "default": 5, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_14-input-asteroidVolume-number"}, {"name": "hardness", "label": "Hardness", "type": "number", "description": "Mining difficulty (1=soft, 5=very hard)", "default": 1, "additionalParams": true, "show": {"inputs.componentType": ["mineable"]}, "id": "Component_14-input-hardness-number"}, {"name": "targetWorld", "label": "Target World", "type": "string", "description": "Target world for portal transportation", "default": "konkordo", "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_14-input-targetWorld-string"}, {"name": "cooldownTime", "label": "Cooldown Time (ms)", "type": "number", "description": "Portal cooldown time in milliseconds", "default": 2000, "additionalParams": true, "show": {"inputs.componentType": ["portal"]}, "id": "Component_14-input-cooldownTime-number"}, {"name": "props", "label": "Additional Properties (JSON)", "type": "json", "description": "Additional component properties as JSON", "optional": true, "additionalParams": true, "id": "Component_14-input-props-json"}], "inputAnchors": [], "inputs": {"componentType": "mineable", "primitive": "box", "color": "#FFFFFF", "scriptName": "myScript.js", "maxCapacity": 20, "currentLoad": "", "fireRate": 2, "damage": 1, "pricePerTon": 10, "interactionRange": 8, "resourceType": "asteroidMass", "maxYield": "4", "asteroidVolume": "8", "hardness": 1, "targetWorld": "konkordo", "cooldownTime": 2000, "props": ""}, "outputAnchors": [{"id": "Component_14-output-Component-UPDLComponent|UPDLNode", "name": "Component", "label": "UPDLComponent", "description": "Component attached to an entity", "type": "UPDLComponent | UPDLNode"}], "outputs": {"Component": ""}, "selected": false}, "width": 300, "height": 328, "selected": false, "positionAbsolute": {"x": -1742.794564116304, "y": 3139.786623861242}, "dragging": false}, {"id": "Space_1", "position": {"x": 1207.8309096297817, "y": -977.8143391949027}, "type": "customNode", "data": {"id": "Space_1", "label": "Space", "version": 1, "name": "Space", "type": "UPDLSpace", "baseClasses": ["UPDLSpace", "UPDLNode"], "tags": ["UPDL"], "category": "UPDL", "description": "Root node for a 3D space that contains global space settings", "inputParams": [{"name": "spaceName", "type": "string", "label": "Space Name", "description": "Name of the space", "default": "My Space", "id": "Space_1-input-spaceName-string"}, {"name": "spaceType", "type": "options", "label": "Space Type", "description": "Classification of the space", "options": [{"label": "Root", "name": "root"}, {"label": "<PERSON><PERSON><PERSON>", "name": "module"}, {"label": "Block", "name": "block"}], "default": "root", "additionalParams": true, "id": "Space_1-input-spaceType-options"}, {"name": "settings", "type": "json", "label": "Settings", "description": "Generic configuration object", "optional": true, "additionalParams": true, "id": "Space_1-input-settings-json"}, {"name": "backgroundColor", "type": "string", "label": "Background Color", "description": "Background color of the space (hex code)", "default": "", "optional": true, "additionalParams": true, "id": "Space_1-input-backgroundColor-string"}, {"name": "skybox", "type": "boolean", "label": "Enable Skybox", "description": "Whether to use a skybox", "default": false, "additionalParams": true, "id": "Space_1-input-skybox-boolean"}, {"name": "skyboxTexture", "type": "string", "label": "Skybox Texture", "description": "URL to the skybox texture (optional)", "optional": true, "additionalParams": true, "show": {"inputs.skybox": [true]}, "id": "Space_1-input-skyboxTexture-string"}, {"name": "fog", "type": "boolean", "label": "Enable Fog", "description": "Whether to use fog effect", "default": false, "additionalParams": true, "id": "Space_1-input-fog-boolean"}, {"name": "fogColor", "type": "string", "label": "Fog Color", "description": "Color of the fog (hex code)", "default": "", "optional": true, "additionalParams": true, "show": {"inputs.fog": [true]}, "id": "Space_1-input-fogColor-string"}, {"name": "fogDensity", "type": "number", "label": "Fog Density", "description": "Density of the fog (0-1)", "default": 0.1, "min": 0, "max": 1, "step": 0.01, "additionalParams": true, "show": {"inputs.fog": [true]}, "id": "Space_1-input-fogDensity-number"}, {"name": "isRootNode", "type": "boolean", "label": "Is Root Node", "description": "Space must be the root node of a UPDL flow", "default": true, "hidden": true, "id": "Space_1-input-isRootNode-boolean"}, {"name": "showPoints", "type": "boolean", "label": "Show Points Counter", "description": "Display points counter in the AR interface", "default": false, "additionalParams": true, "id": "Space_1-input-showPoints-boolean"}, {"name": "collectLeadName", "type": "boolean", "label": "Collect Name", "description": "Collect participant name for quiz data", "default": false, "additionalParams": true, "id": "Space_1-input-collectLeadName-boolean"}, {"name": "collectLeadEmail", "type": "boolean", "label": "Collect Email", "description": "Collect participant email for quiz data", "default": false, "additionalParams": true, "id": "Space_1-input-collectLeadEmail-boolean"}, {"name": "collectLeadPhone", "type": "boolean", "label": "Collect Phone", "description": "Collect participant phone for quiz data", "default": false, "additionalParams": true, "id": "Space_1-input-collect<PERSON>eadPhone-boolean"}], "inputAnchors": [{"label": "Spaces", "name": "spaces", "type": "UPDLSpace", "description": "Connect Space nodes to create space chains", "list": true, "optional": true, "id": "Space_1-input-spaces-UPDLSpace"}, {"label": "Datas", "name": "data", "type": "UPDLData", "description": "Connect Data nodes for quiz questions, answers, and logic", "list": true, "optional": true, "id": "Space_1-input-data-UPDLData"}, {"label": "Objects", "name": "objects", "type": "UPDLObject", "description": "Connect Object nodes to add them to the space", "list": true, "optional": true, "id": "Space_1-input-objects-UPDLObject"}, {"label": "Lights", "name": "lights", "type": "UPDLLight", "description": "Connect Light nodes to add them to the space", "list": true, "optional": true, "id": "Space_1-input-lights-UPDLLight"}, {"label": "Cameras", "name": "cameras", "type": "UPDLCamera", "description": "Connect Camera nodes to add them to the space", "list": true, "optional": true, "id": "Space_1-input-cameras-UPDLCamera"}, {"label": "Entities", "name": "entities", "type": "UPDLEntity", "description": "Connect Entity nodes to add them to the space", "list": true, "optional": true, "id": "Space_1-input-entities-UPDLEntity"}, {"label": "Universo", "name": "universo", "type": "UPDLUniverso", "description": "Connect a Universo node for global settings", "optional": true, "id": "Space_1-input-universo-UPDLUniverso"}], "inputs": {"spaces": "", "spaceName": "Start", "spaceType": "root", "settings": "", "backgroundColor": "", "skybox": "", "skyboxTexture": "", "fog": "", "fogColor": "", "fogDensity": 0.1, "isRootNode": true, "showPoints": "", "collectLeadName": true, "collectLeadEmail": "", "collectLeadPhone": "", "data": "", "objects": "", "lights": "", "cameras": "", "entities": "", "universo": ""}, "outputAnchors": [{"id": "Space_1-output-Space-UPDLSpace|UPDLNode", "name": "Space", "label": "UPDLSpace", "description": "Root node for a 3D space that contains global space settings", "type": "UPDLSpace | UPDLNode"}], "outputs": {"Space": ""}, "selected": false}, "width": 300, "height": 686, "selected": false, "positionAbsolute": {"x": 1207.8309096297817, "y": -977.8143391949027}, "dragging": false}], "edges": [{"source": "Entity_0", "sourceHandle": "Entity_0-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_0-Entity_0-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_0", "sourceHandle": "Component_0-output-Component-UPDLComponent|UPDLNode", "target": "Entity_0", "targetHandle": "Entity_0-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_0-Component_0-output-Component-UPDLComponent|UPDLNode-Entity_0-Entity_0-input-components-UPDLComponent"}, {"source": "Component_1", "sourceHandle": "Component_1-output-Component-UPDLComponent|UPDLNode", "target": "Entity_0", "targetHandle": "Entity_0-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_1-Component_1-output-Component-UPDLComponent|UPDLNode-Entity_0-Entity_0-input-components-UPDLComponent"}, {"source": "Component_2", "sourceHandle": "Component_2-output-Component-UPDLComponent|UPDLNode", "target": "Entity_0", "targetHandle": "Entity_0-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_2-Component_2-output-Component-UPDLComponent|UPDLNode-Entity_0-Entity_0-input-components-UPDLComponent"}, {"source": "Entity_1", "sourceHandle": "Entity_1-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_1-Entity_1-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_3", "sourceHandle": "Component_3-output-Component-UPDLComponent|UPDLNode", "target": "Entity_1", "targetHandle": "Entity_1-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_3-Component_3-output-Component-UPDLComponent|UPDLNode-Entity_1-Entity_1-input-components-UPDLComponent"}, {"source": "Component_4", "sourceHandle": "Component_4-output-Component-UPDLComponent|UPDLNode", "target": "Entity_1", "targetHandle": "Entity_1-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_4-Component_4-output-Component-UPDLComponent|UPDLNode-Entity_1-Entity_1-input-components-UPDLComponent"}, {"source": "Entity_2", "sourceHandle": "Entity_2-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_2-Entity_2-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_5", "sourceHandle": "Component_5-output-Component-UPDLComponent|UPDLNode", "target": "Entity_2", "targetHandle": "Entity_2-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_5-Component_5-output-Component-UPDLComponent|UPDLNode-Entity_2-Entity_2-input-components-UPDLComponent"}, {"source": "Entity_3", "sourceHandle": "Entity_3-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_3-Entity_3-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_7", "sourceHandle": "Component_7-output-Component-UPDLComponent|UPDLNode", "target": "Entity_3", "targetHandle": "Entity_3-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_7-Component_7-output-Component-UPDLComponent|UPDLNode-Entity_3-Entity_3-input-components-UPDLComponent"}, {"source": "Component_8", "sourceHandle": "Component_8-output-Component-UPDLComponent|UPDLNode", "target": "Entity_3", "targetHandle": "Entity_3-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_8-Component_8-output-Component-UPDLComponent|UPDLNode-Entity_3-Entity_3-input-components-UPDLComponent"}, {"source": "Data_0", "sourceHandle": "Data_0-output-Data-UPDLData|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-data-UPDLData", "type": "buttonedge", "id": "Data_0-Data_0-output-Data-UPDLData|UPDLNode-Space_0-Space_0-input-data-UPDLData"}, {"source": "Data_1", "sourceHandle": "Data_1-output-Data-UPDLData|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-data-UPDLData", "type": "buttonedge", "id": "Data_1-Data_1-output-Data-UPDLData|UPDLNode-Space_0-Space_0-input-data-UPDLData"}, {"source": "Component_9", "sourceHandle": "Component_9-output-Component-UPDLComponent|UPDLNode", "target": "Entity_2", "targetHandle": "Entity_2-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_9-Component_9-output-Component-UPDLComponent|UPDLNode-Entity_2-Entity_2-input-components-UPDLComponent"}, {"source": "Entity_4", "sourceHandle": "Entity_4-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_4-Entity_4-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_6", "sourceHandle": "Component_6-output-Component-UPDLComponent|UPDLNode", "target": "Entity_4", "targetHandle": "Entity_4-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_6-Component_6-output-Component-UPDLComponent|UPDLNode-Entity_4-Entity_4-input-components-UPDLComponent"}, {"source": "Component_10", "sourceHandle": "Component_10-output-Component-UPDLComponent|UPDLNode", "target": "Entity_4", "targetHandle": "Entity_4-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_10-Component_10-output-Component-UPDLComponent|UPDLNode-Entity_4-Entity_4-input-components-UPDLComponent"}, {"source": "Component_11", "sourceHandle": "Component_11-output-Component-UPDLComponent|UPDLNode", "target": "Entity_5", "targetHandle": "Entity_5-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_11-Component_11-output-Component-UPDLComponent|UPDLNode-Entity_5-Entity_5-input-components-UPDLComponent"}, {"source": "Component_12", "sourceHandle": "Component_12-output-Component-UPDLComponent|UPDLNode", "target": "Entity_5", "targetHandle": "Entity_5-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_12-Component_12-output-Component-UPDLComponent|UPDLNode-Entity_5-Entity_5-input-components-UPDLComponent"}, {"source": "Entity_5", "sourceHandle": "Entity_5-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_5-Entity_5-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Component_13", "sourceHandle": "Component_13-output-Component-UPDLComponent|UPDLNode", "target": "Entity_6", "targetHandle": "Entity_6-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_13-Component_13-output-Component-UPDLComponent|UPDLNode-Entity_6-Entity_6-input-components-UPDLComponent"}, {"source": "Component_14", "sourceHandle": "Component_14-output-Component-UPDLComponent|UPDLNode", "target": "Entity_6", "targetHandle": "Entity_6-input-components-UPDLComponent", "type": "buttonedge", "id": "Component_14-Component_14-output-Component-UPDLComponent|UPDLNode-Entity_6-Entity_6-input-components-UPDLComponent"}, {"source": "Entity_6", "sourceHandle": "Entity_6-output-Entity-UPDLEntity|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-entities-UPDLEntity", "type": "buttonedge", "id": "Entity_6-Entity_6-output-Entity-UPDLEntity|UPDLNode-Space_0-Space_0-input-entities-UPDLEntity"}, {"source": "Space_1", "sourceHandle": "Space_1-output-Space-UPDLSpace|UPDLNode", "target": "Space_0", "targetHandle": "Space_0-input-spaces-UPDLSpace", "type": "buttonedge", "id": "Space_1-Space_1-output-Space-UPDLSpace|UPDLNode-Space_0-Space_0-input-spaces-UPDLSpace"}]}