{"name": "flowise", "version": "2.2.8", "description": "Universo Platformo React Server", "main": "dist/index", "types": "dist/index.d.ts", "bin": {"flowise": "./bin/run"}, "files": ["bin", "marketplaces", "dist", "npm-shrinkwrap.json", "oclif.manifest.json", "oauth2.html"], "oclif": {"bin": "flowise", "commands": "./dist/commands"}, "scripts": {"build": "tsc", "start": "run-script-os", "clean": "<PERSON><PERSON><PERSON> dist", "nuke": "rimraf dist node_modules .turbo", "start:windows": "cd bin && run start", "start:default": "cd bin && ./run start", "start-worker:windows": "cd bin && run worker", "start-worker:default": "cd bin && ./run worker", "dev": "nodemon", "oclif-dev": "run-script-os", "oclif-dev:windows": "cd bin && dev start", "oclif-dev:default": "cd bin && ./dev start", "postpack": "shx rm -f oclif.manifest.json", "prepack": "pnpm build && oclif manifest && oclif readme", "typeorm": "typeorm-ts-node-commonjs", "typeorm:migration-generate": "pnpm typeorm migration:generate -d ./src/utils/typeormDataSource.ts", "typeorm:migration-run": "pnpm typeorm migration:run -d ./src/utils/typeormDataSource.ts", "watch": "tsc --watch", "version": "oclif readme && git add README.md", "cypress:open": "cypress open", "cypress:run": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "cypress:ci": "START_SERVER_AND_TEST_INSECURE=1 start-server-and-test start https-get://localhost:3000 cypress:run", "test": "jest"}, "keywords": [], "homepage": "https://universo.pro", "author": {"name": "<PERSON> and Tek<PERSON>ko<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.699.0", "@google-cloud/logging-winston": "^6.0.0", "@oclif/core": "4.0.7", "@opentelemetry/api": "^1.3.0", "@opentelemetry/auto-instrumentations-node": "^0.52.0", "@opentelemetry/core": "1.27.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.54.0", "@opentelemetry/exporter-metrics-otlp-http": "0.54.0", "@opentelemetry/exporter-metrics-otlp-proto": "0.54.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.54.0", "@opentelemetry/exporter-trace-otlp-http": "0.54.0", "@opentelemetry/exporter-trace-otlp-proto": "0.54.0", "@opentelemetry/resources": "1.27.0", "@opentelemetry/sdk-metrics": "1.27.0", "@opentelemetry/sdk-node": "^0.54.0", "@opentelemetry/sdk-trace-base": "1.27.0", "@opentelemetry/semantic-conventions": "1.27.0", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@universo/metaverse-srv": "workspace:*", "@universo/multiplayer-srv": "workspace:*", "@universo/profile-srv": "workspace:*", "@universo/publish-srv": "workspace:*", "@universo/space-builder-srv": "workspace:*", "@universo/uniks-srv": "workspace:*", "async-mutex": "^0.4.0", "axios": "1.7.9", "bull-board": "^2.1.3", "bullmq": "^5.42.0", "content-disposition": "0.5.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.0.0", "express": "^4.17.3", "express-basic-auth": "^1.2.1", "express-rate-limit": "^6.9.0", "flowise-components": "workspace:^", "flowise-nim-container-manager": "^1.0.11", "flowise-ui": "workspace:^", "global-agent": "^3.0.0", "http-errors": "^2.0.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "langchainhub": "^0.0.11", "lodash": "^4.17.21", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "multer": "^1.4.5-lts.1", "multer-cloud-storage": "^4.0.0", "multer-s3": "^3.0.1", "mysql2": "^3.11.3", "openai": "^4.82.0", "pg": "^8.11.1", "posthog-node": "^3.5.0", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "s3-streamlogger": "^1.11.0", "sanitize-html": "^2.11.0", "sqlite3": "^5.1.6", "typeorm": "^0.3.6", "uuid": "^9.0.1", "winston": "^3.9.0"}, "devDependencies": {"@types/content-disposition": "0.5.8", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.7", "@types/multer-s3": "^3.0.3", "@types/sanitize-html": "^2.9.5", "concurrently": "^7.1.0", "cypress": "^13.13.0", "nodemon": "^2.0.22", "oclif": "^3", "rimraf": "^5.0.5", "run-script-os": "^1.1.6", "shx": "^0.3.3", "start-server-and-test": "^2.0.3", "ts-node": "^10.7.0", "tsc-watch": "^6.0.4", "typescript": "^5.4.5"}}