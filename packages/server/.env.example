PORT=3000

# APIKEY_STORAGE_TYPE=json (json | db)
# APIKEY_PATH=/your_api_key_path/.flowise
 

############################################################################################################
############################################################################################################
########################################### UNIVERSO PLATFORMO #############################################
############################################################################################################
############################################################################################################

############################################################################################################
########################################### DATABASE #######################################################
############################################################################################################

# DATABASE_PATH=/your_database_path/.flowise
# DATABASE_TYPE=postgres
# DATABASE_PORT=5432
# DATABASE_HOST=""
# DATABASE_NAME=flowise
# DATABASE_USER=root
# DATABASE_PASSWORD=mypassword
# DATABASE_SSL=true
# DATABASE_SSL_KEY_BASE64=<Self signed certificate in BASE64>


############################################################################################################
########################################### SUPABASE #######################################################
############################################################################################################

# SUPABASE_URL=
# SUPABASE_ANON_KEY=
# SUPABASE_JWT_SECRET=


############################################################################################################
############################################## SECRET KEYS #################################################
############################################################################################################

# SECRETKEY_STORAGE_TYPE=local #(local | aws)
# SECRETKEY_PATH=/your_api_key_path/.flowise
# FLOWISE_SECRETKEY_OVERWRITE=myencryptionkey
# SECRETKEY_AWS_ACCESS_KEY=<your-access-key>
# SECRETKEY_AWS_SECRET_KEY=<your-secret-key>
# SECRETKEY_AWS_REGION=us-west-2
# SECRETKEY_AWS_NAME=FlowiseEncryptionKey


############################################################################################################
############################################## LOGGING #####################################################
############################################################################################################

# DEBUG=true
# LOG_PATH=/your_log_path/.flowise/logs
# LOG_LEVEL=info (error | warn | info | verbose | debug)
# TOOL_FUNCTION_BUILTIN_DEP=crypto,fs
# TOOL_FUNCTION_EXTERNAL_DEP=moment,lodash


############################################################################################################
############################################## STORAGE #####################################################
############################################################################################################

# STORAGE_TYPE=local (local | s3)
# BLOB_STORAGE_PATH=/your_storage_path/.flowise/storage
# S3_STORAGE_BUCKET_NAME=flowise
# S3_STORAGE_ACCESS_KEY_ID=<your-access-key>
# S3_STORAGE_SECRET_ACCESS_KEY=<your-secret-key>
# S3_STORAGE_REGION=us-west-2
# S3_ENDPOINT_URL=<custom-s3-endpoint-url>
# S3_FORCE_PATH_STYLE=false
# GOOGLE_CLOUD_STORAGE_CREDENTIAL=/the/keyfilename/path
# GOOGLE_CLOUD_STORAGE_PROJ_ID=<your-gcp-project-id>
# GOOGLE_CLOUD_STORAGE_BUCKET_NAME=<the-bucket-name>
# GOOGLE_CLOUD_UNIFORM_BUCKET_ACCESS=true


############################################################################################################
############################################## SETTINGS ####################################################
############################################################################################################

# NUMBER_OF_PROXIES= 1
# CORS_ORIGINS=*
# IFRAME_ORIGINS=*

# FLOWISE_USERNAME=user
# FLOWISE_PASSWORD=1234
# FLOWISE_FILE_SIZE_LIMIT=50mb

# LANGCHAIN_TRACING_V2=true
# LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
# LANGCHAIN_API_KEY=your_api_key
# LANGCHAIN_PROJECT=your_project

# DISABLE_FLOWISE_TELEMETRY=true

# Uncomment the following line to enable model list config, load the list of models from your local config file
# see https://raw.githubusercontent.com/FlowiseAI/Flowise/main/packages/components/models.json for the format
# MODEL_LIST_CONFIG_JSON=/your_model_list_config_file_path

# SHOW_COMMUNITY_NODES=true
# DISABLED_NODES=bufferMemory,chatOpenAI (comma separated list of node names to disable)


############################################################################################################
########################################### METRICS COLLECTION #############################################
############################################################################################################

# ENABLE_METRICS=false
# METRICS_PROVIDER=prometheus # prometheus | open_telemetry
# METRICS_INCLUDE_NODE_METRICS=true  # default is true
# METRICS_SERVICE_NAME=FlowiseAI

# ONLY NEEDED if METRICS_PROVIDER=open_telemetry
# METRICS_OPEN_TELEMETRY_METRIC_ENDPOINT=http://localhost:4318/v1/metrics
# METRICS_OPEN_TELEMETRY_PROTOCOL=http # http | grpc | proto (default is http)
# METRICS_OPEN_TELEMETRY_DEBUG=true # default is false


############################################################################################################
############################################### PROXY ######################################################
############################################################################################################

# Uncomment the following lines to enable global agent proxy
# see https://www.npmjs.com/package/global-agent for more details
# GLOBAL_AGENT_HTTP_PROXY=CorporateHttpProxyUrl
# GLOBAL_AGENT_HTTPS_PROXY=CorporateHttpsProxyUrl
# GLOBAL_AGENT_NO_PROXY=ExceptionHostsToBypassProxyIfNeeded


############################################################################################################
########################################### QUEUE CONFIGURATION ############################################
############################################################################################################

# MODE=queue #(queue | main)
# QUEUE_NAME=flowise-queue
# QUEUE_REDIS_EVENT_STREAM_MAX_LEN=100000
# WORKER_CONCURRENCY=100000
# REMOVE_ON_AGE=86400
# REMOVE_ON_COUNT=10000
# REDIS_URL=
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_USERNAME=
# REDIS_PASSWORD=
# REDIS_TLS=
# REDIS_CERT=
# REDIS_KEY=
# REDIS_CA=


############################################################################################################
########################################### MULTIPLAYER ################################################
############################################################################################################

# Enable Colyseus multiplayer server (true/false)
# ENABLE_MULTIPLAYER_SERVER=false

# Colyseus server port (default: 2567)
# MULTIPLAYER_SERVER_PORT=2567

# Colyseus server host (default: localhost)
# MULTIPLAYER_SERVER_HOST=localhost


############################################################################################################
########################################### SPACE BUILDER ##################################################
############################################################################################################

# Enable test mode in Space Builder UI (true/false)
# SPACE_BUILDER_TEST_MODE=false

# Optional: test-only key used by Space Builder test mode
# If set and SPACE_BUILDER_TEST_MODE=true, you can select "Test mode" in the generator dialog.

# When enabled, user credential-based models are disabled in UI and server forcibly uses test providers
# SPACE_BUILDER_DISABLE_USER_CREDENTIALS=false

# Enable specific test providers and configure their models/keys/base URLs
# SPACE_BUILDER_TEST_ENABLE_GROQ=true
# GROQ_TEST_MODEL=llama-3.3-70b-versatile
# GROQ_TEST_API_KEY=
# GROQ_TEST_BASE_URL=https://api.groq.com/openai/v1

# SPACE_BUILDER_TEST_ENABLE_OPENAI=false
# OPENAI_TEST_MODEL=gpt-4o-mini
# OPENAI_TEST_API_KEY=
# OPENAI_TEST_BASE_URL=

# SPACE_BUILDER_TEST_ENABLE_OPENROUTER=false
# OPENROUTER_TEST_MODEL=meta-llama/llama-3.1-70b-instruct
# OPENROUTER_TEST_API_KEY=
# OPENROUTER_TEST_BASE_URL=https://openrouter.ai/api/v1
# OPENROUTER_TEST_REFERER=
# OPENROUTER_TEST_TITLE=

# SPACE_BUILDER_TEST_ENABLE_CEREBRAS=false
# CEREBRAS_TEST_MODEL=llama3.1-70b
# CEREBRAS_TEST_API_KEY=
# CEREBRAS_TEST_BASE_URL=https://api.cerebras.ai/v1

# SPACE_BUILDER_TEST_ENABLE_GIGACHAT=false
# GIGACHAT_TEST_MODEL=GigaChat-Pro
# GIGACHAT_TEST_API_KEY=
# GIGACHAT_TEST_BASE_URL=

# SPACE_BUILDER_TEST_ENABLE_YANDEXGPT=false
# YANDEXGPT_TEST_MODEL=yandexgpt-lite
# YANDEXGPT_TEST_API_KEY=
# YANDEXGPT_TEST_BASE_URL=

# SPACE_BUILDER_TEST_ENABLE_GOOGLE=false
# GOOGLE_TEST_MODEL=gemini-1.5-pro
# GOOGLE_TEST_API_KEY=
# GOOGLE_TEST_BASE_URL=

# SPACE_BUILDER_TEST_ENABLE_CUSTOM=false
# CUSTOM_TEST_NAME=MyProvider
# CUSTOM_TEST_MODEL=my-model
# CUSTOM_TEST_API_KEY=
# CUSTOM_TEST_BASE_URL=
# CUSTOM_TEST_EXTRA_HEADERS_JSON=