{"name": "@universo/multiplayer-srv", "version": "0.1.0", "description": "Colyseus multiplayer server for Universo MMOOMM", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["colyseus", "multiplayer", "mmoomm", "universo"], "author": "Universo Platformo", "license": "MIT", "dependencies": {"colyseus": "^0.14.0", "@colyseus/schema": "^1.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "ts-node": "^10.9.0", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0", "rimraf": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}