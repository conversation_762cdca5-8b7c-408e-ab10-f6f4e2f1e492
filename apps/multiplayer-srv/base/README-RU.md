# Universo MMOOMM Multiplayer Server

Многопользовательский сервер на базе Colyseus для космического геймплея Universo MMOOMM.

## Возможности

- **Многопользовательская игра в реальном времени**: До 16 игроков в комнате
- **Серверная авторизация игрового процесса**: Добыча и торговля проверяются на сервере
- **Синхронизация сущностей**: Астероиды, станции и врата синхронизируются между клиентами
- **Простая аутентификация**: Идентификация игроков по имени
- **Валидация позиций**: Предотвращает читерство с ограничениями движения

## Быстрый старт

### Разработка

```bash
# Установить зависимости
pnpm install

# Запустить сервер разработки с горячей перезагрузкой
pnpm dev

# Сервер запустится на ws://localhost:2567
```

### Продакшн

```bash
# Собрать TypeScript
pnpm build

# Запустить продакшн сервер
pnpm start
```

## Конфигурация комнаты

- **Тип комнаты**: `mmoomm`
- **Максимум игроков**: 16
- **Порт**: 2567 (настраивается через переменную окружения PORT)

## Подключение клиента

```javascript
const client = new Colyseus.Client('ws://localhost:2567');
const room = await client.joinOrCreate('mmoomm', { 
    name: 'PlayerName' 
});
```

## Протокол сообщений

### Клиент → Сервер

- `updateTransform`: Обновить позицию/поворот игрока
- `startMining`: Запросить действие добычи
- `sellAll`: Продать весь инвентарь на ближайшей станции

### Сервер → Клиент

- Синхронизация состояния через схемы Colyseus
- Обновления в реальном времени для игроков, астероидов, станций

## Архитектура

- **MMOOMMRoom**: Основной класс комнаты, обрабатывающий игровую логику
- **Схемы**: Типобезопасная синхронизация состояния
  - `PlayerSchema`: Позиция игрока, инвентарь, кредиты
  - `EntitySchema`: Астероиды, станции, врата
  - `MMOOMMRoomState`: Полное состояние комнаты

## Интеграция

Этот сервер интегрируется с шаблоном MMOOMM PlayCanvas при обнаружении многопользовательского режима (Space с collectLeadName=true + подключенный Space).

## Переменные окружения

- `PORT`: Порт сервера (по умолчанию: 2567)
- `NODE_ENV`: Режим окружения (development/production)
