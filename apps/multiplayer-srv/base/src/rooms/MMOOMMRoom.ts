// Universo Platformo | MMOOMM Room for Colyseus
import { Room, Client } from "colyseus"
import { MMO<PERSON><PERSON>oomState, PlayerSchema, EntitySchema } from "../schemas"

/**
 * MMOOMM Room handles multiplayer space gameplay
 */
export class M<PERSON><PERSON>MRoom extends Room<MMOOMMRoomState> {
    maxClients = 16
    state = new MMOOMMRoomState()

    onCreate(options: any) {
        console.log('[MMOOMMRoom] Creating room with options:', options)
        
        // Initialize entities from UPDL data if provided
        this.initializeEntities(options.entities || [])
        
        // Setup message handlers
        this.onMessage("updateTransform", this.handleUpdateTransform.bind(this))
        this.onMessage("startMining", this.handleStartMining.bind(this))
        this.onMessage("sellAll", this.handleSellAll.bind(this))
        
        console.log('[MMOOMMRoom] Room created successfully')
    }
    
    onJoin(client: Client, options: any) {
        console.log(`[MMOOMMRoom] Player joining: ${client.sessionId}`)
        
        const player = new PlayerSchema()
        player.name = options.name || `Player${client.sessionId.slice(0, 4)}`
        
        // Random spawn position in space
        player.x = (Math.random() - 0.5) * 100
        player.y = 2
        player.z = (Math.random() - 0.5) * 100
        
        // Starting resources
        player.inventory = 0
        player.credits = 100
        
        this.state.players.set(client.sessionId, player)
        this.state.currentPlayers++
        
        console.log(`[MMOOMMRoom] Player ${player.name} joined at position (${player.x}, ${player.y}, ${player.z})`)
    }
    
    onLeave(client: Client, consented: boolean) {
        console.log(`[MMOOMMRoom] Player leaving: ${client.sessionId}`)
        
        this.state.players.delete(client.sessionId)
        this.state.currentPlayers--
        
        console.log(`[MMOOMMRoom] Player left, current players: ${this.state.currentPlayers}`)
    }
    
    onDispose() {
        console.log('[MMOOMMRoom] Room disposed')
    }
    
    /**
     * Initialize entities (asteroids, stations, gates) from UPDL data
     */
    private initializeEntities(entities: any[]) {
        console.log('[MMOOMMRoom] Initializing entities:', entities.length)
        
        // Create default entities if none provided
        if (entities.length === 0) {
            this.createDefaultEntities()
            return
        }
        
        // Process UPDL entities
        entities.forEach((entity, index) => {
            const entitySchema = new EntitySchema()
            entitySchema.id = entity.id || `entity_${index}`
            entitySchema.entityType = entity.entityType || 'asteroid'
            
            // Position from UPDL or random
            entitySchema.x = entity.position?.x || (Math.random() - 0.5) * 200
            entitySchema.y = entity.position?.y || 0
            entitySchema.z = entity.position?.z || (Math.random() - 0.5) * 200
            
            // Scale from UPDL or default
            entitySchema.scaleX = entity.scale?.x || 1
            entitySchema.scaleY = entity.scale?.y || 1
            entitySchema.scaleZ = entity.scale?.z || 1
            
            // Type-specific properties
            if (entity.entityType === 'asteroid') {
                entitySchema.resourceLeft = entity.resourceLeft || 100
                entitySchema.material = entity.material || 'iron'
                this.state.asteroids.set(entitySchema.id, entitySchema)
            } else if (entity.entityType === 'station') {
                entitySchema.buyPrice = entity.buyPrice || 10
                this.state.stations.set(entitySchema.id, entitySchema)
            } else if (entity.entityType === 'gate') {
                this.state.gates.set(entitySchema.id, entitySchema)
            }
        })
        
        console.log(`[MMOOMMRoom] Initialized ${this.state.asteroids.size} asteroids, ${this.state.stations.size} stations, ${this.state.gates.size} gates`)
    }
    
    /**
     * Create default entities for empty rooms
     */
    private createDefaultEntities() {
        // Create some default asteroids
        for (let i = 0; i < 5; i++) {
            const asteroid = new EntitySchema()
            asteroid.id = `asteroid_${i}`
            asteroid.entityType = 'asteroid'
            asteroid.x = (Math.random() - 0.5) * 150
            asteroid.y = 0
            asteroid.z = (Math.random() - 0.5) * 150
            asteroid.resourceLeft = 100
            asteroid.material = ['iron', 'copper', 'gold'][Math.floor(Math.random() * 3)]
            this.state.asteroids.set(asteroid.id, asteroid)
        }
        
        // Create a default station
        const station = new EntitySchema()
        station.id = 'station_0'
        station.entityType = 'station'
        station.x = 0
        station.y = 0
        station.z = 50
        station.buyPrice = 10
        this.state.stations.set(station.id, station)
        
        console.log('[MMOOMMRoom] Created default entities: 5 asteroids, 1 station')
    }

    /**
     * Handle player transform updates
     */
    private handleUpdateTransform(client: Client, data: any) {
        const player = this.state.players.get(client.sessionId)
        if (!player || !data) return

        // Update player position and rotation with validation
        if (typeof data.x === 'number') player.x = Math.max(-500, Math.min(500, data.x))
        if (typeof data.y === 'number') player.y = Math.max(-100, Math.min(100, data.y))
        if (typeof data.z === 'number') player.z = Math.max(-500, Math.min(500, data.z))
        if (typeof data.rx === 'number') player.rx = data.rx
        if (typeof data.ry === 'number') player.ry = data.ry
        if (typeof data.rz === 'number') player.rz = data.rz

        player.lastUpdate = Date.now()
    }

    /**
     * Handle mining requests (server-authoritative)
     */
    private handleStartMining(client: Client, data: any) {
        const player = this.state.players.get(client.sessionId)
        if (!player) return

        // Find nearest asteroid
        const nearestAsteroid = this.findNearestAsteroid(player.x, player.y, player.z)
        if (!nearestAsteroid) return

        const distance = this.calculateDistance(player, nearestAsteroid)
        const miningRange = 75 // Mining range

        if (distance <= miningRange && nearestAsteroid.resourceLeft > 0) {
            // Mine resources
            const mineAmount = 1.5
            nearestAsteroid.resourceLeft = Math.max(0, nearestAsteroid.resourceLeft - mineAmount)
            player.inventory += mineAmount
            nearestAsteroid.lastUpdate = Date.now()

            console.log(`[MMOOMMRoom] Player ${player.name} mined ${mineAmount} from ${nearestAsteroid.id}, remaining: ${nearestAsteroid.resourceLeft}`)
        }
    }

    /**
     * Handle selling all inventory at nearest station
     */
    private handleSellAll(client: Client) {
        const player = this.state.players.get(client.sessionId)
        if (!player || player.inventory <= 0) return

        // Find nearest station
        const nearestStation = this.findNearestStation(player.x, player.y, player.z)
        if (!nearestStation) return

        const distance = this.calculateDistance(player, nearestStation)
        const tradingRange = 50 // Trading range

        if (distance <= tradingRange) {
            // Sell all inventory
            const sellAmount = player.inventory
            const totalCredits = sellAmount * nearestStation.buyPrice

            player.credits += totalCredits
            player.inventory = 0

            console.log(`[MMOOMMRoom] Player ${player.name} sold ${sellAmount} units for ${totalCredits} credits`)
        }
    }

    /**
     * Find nearest asteroid to player
     */
    private findNearestAsteroid(x: number, y: number, z: number): EntitySchema | null {
        let nearest: EntitySchema | null = null
        let minDistance = Infinity

        this.state.asteroids.forEach((asteroid) => {
            if (asteroid.resourceLeft <= 0) return // Skip depleted asteroids

            const distance = Math.sqrt(
                Math.pow(asteroid.x - x, 2) +
                Math.pow(asteroid.y - y, 2) +
                Math.pow(asteroid.z - z, 2)
            )

            if (distance < minDistance) {
                minDistance = distance
                nearest = asteroid
            }
        })

        return nearest
    }

    /**
     * Find nearest station to player
     */
    private findNearestStation(x: number, y: number, z: number): EntitySchema | null {
        let nearest: EntitySchema | null = null
        let minDistance = Infinity

        this.state.stations.forEach((station) => {
            const distance = Math.sqrt(
                Math.pow(station.x - x, 2) +
                Math.pow(station.y - y, 2) +
                Math.pow(station.z - z, 2)
            )

            if (distance < minDistance) {
                minDistance = distance
                nearest = station
            }
        })

        return nearest
    }

    /**
     * Calculate distance between player and entity
     */
    private calculateDistance(player: PlayerSchema, entity: EntitySchema): number {
        return Math.sqrt(
            Math.pow(entity.x - player.x, 2) +
            Math.pow(entity.y - player.y, 2) +
            Math.pow(entity.z - player.z, 2)
        )
    }
}
